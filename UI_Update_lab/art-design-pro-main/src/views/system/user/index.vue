<template>
  <div class="art-full-height">
    <div class="user-container">
      <!-- 左侧部门树 -->
      <div class="left-sidebar">
        <ElCard class="art-table-card" shadow="never">
          <template #header>
            <span>部门列表</span>
          </template>
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
          <el-tree
            ref="deptTreeRef"
            :data="deptOptions"
            :props="{ label: 'deptName', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            node-key="deptId"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </ElCard>
      </div>

      <!-- 右侧用户列表 -->
      <div class="right-content art-full-height">
        <UserSearch v-model="searchForm" @search="handleSearch" @reset="resetSearchParams" />

        <ElCard class="art-table-card" shadow="never">
          <ArtTableHeader :columns="columnChecks" @refresh="refreshData">
            <template #left>
              <el-button
                v-ripple
                v-auth="'system:user:add'"
                type="primary"
                @click="showDialog('add')"
              >
                新增用户
              </el-button>
              <el-button
                v-ripple
                v-auth="'system:user:edit'"
                type="success"
                :disabled="single"
                @click="() => handleUpdate()"
              >
                修改
              </el-button>
              <el-button
                v-ripple
                v-auth="'system:user:remove'"
                type="danger"
                :disabled="multiple"
                @click="() => handleDelete()"
              >
                删除
              </el-button>
              <el-button v-ripple v-auth="'system:user:import'" type="info" @click="handleImport">
                导入
              </el-button>
              <el-button
                v-ripple
                v-auth="'system:user:export'"
                type="warning"
                @click="handleExport"
              >
                导出
              </el-button>
            </template>
          </ArtTableHeader>

          <ArtTable
            rowKey="userId"
            :loading="loading"
            :data="userList"
            :columns="columns"
            :pagination="pagination"
            @pagination:size-change="handleSizeChange"
            @pagination:current-change="handleCurrentChange"
            @selection-change="handleSelectionChange"
          >
            <template #status="{ row }">
              <el-switch
                v-model="row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(row)"
              />
            </template>

            <template #createTime="{ row }">
              <span>{{ formatTime(row.createTime) }}</span>
            </template>

            <template #action="{ row }">
              <template v-if="row.userId !== 1">
                <el-tooltip content="修改" placement="top">
                  <el-button
                    v-ripple
                    v-auth="'system:user:edit'"
                    link
                    type="primary"
                    @click="handleUpdate(row)"
                  >
                    编辑
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button
                    v-ripple
                    v-auth="'system:user:remove'"
                    link
                    type="danger"
                    @click="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </el-tooltip>
                <el-tooltip content="重置密码" placement="top">
                  <el-button
                    v-ripple
                    v-auth="'system:user:resetPwd'"
                    link
                    type="warning"
                    @click="handleResetPwd(row)"
                  >
                    重置
                  </el-button>
                </el-tooltip>
                <el-tooltip content="分配角色" placement="top">
                  <el-button
                    v-ripple
                    v-auth="'system:user:edit'"
                    link
                    type="info"
                    @click="handleAuthRole(row)"
                  >
                    角色
                  </el-button>
                </el-tooltip>
              </template>
            </template>
          </ArtTable>

          <!-- 用户弹窗 -->
          <UserForm
            :visible="dialogVisible"
            :type="dialogType"
            :user-data="currentUserData"
            @update:visible="dialogVisible = $event"
            @submit="handleDialogSubmit"
          />
        </ElCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue'
  import { ElMessageBox, ElMessage, ElCard } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { UserApi } from '@/api/system/user'
  import type { User } from '@/types/system/user'
  import type { Dept } from '@/types/system/dept'
  import UserSearch from './components/UserSearch.vue'
  import UserForm from './components/UserForm.vue'
  import { formatTime } from '@/utils/date'

  defineOptions({ name: 'SystemUser' })

  // 部门树相关
  const deptName = ref('')
  const deptOptions = ref<Dept[]>([])
  const deptTreeRef = ref()

  // 弹窗相关
  const dialogType = ref<'add' | 'edit'>('add')
  const dialogVisible = ref(false)
  const currentUserData = ref<Partial<User>>({})

  // 搜索表单
  const searchForm = ref({
    userName: '',
    phonenumber: '',
    status: '',
    beginTime: '',
    endTime: ''
  })

  // 选择状态
  const single = ref(true)
  const multiple = ref(true)
  const ids = ref<number[]>([])

  // 使用useTable组合式函数
  const {
    data: userList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange
  } = useTable<User>({
    core: {
      apiFn: UserApi.getUserList,
      apiParams: {
        current: 1,
        size: 20,
        userName: '',
        phonenumber: '',
        status: undefined,
        deptId: undefined
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 50,
          checked: true,
          disabled: true
        },
        {
          prop: 'userId',
          label: '用户编号',
          width: 80,
          checked: true
        },
        {
          prop: 'userName',
          label: '用户名称',
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'nickName',
          label: '用户昵称',
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'dept.deptName',
          label: '部门',
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'phonenumber',
          label: '手机号码',
          width: 120,
          checked: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          checked: true,
          useSlot: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 160,
          checked: true,
          useSlot: true
        },
        {
          prop: 'action',
          label: '操作',
          width: 200,
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  /**
   * 查询部门下拉树结构
   */
  async function getDeptTree() {
    try {
      const response = await UserApi.deptTreeSelect()
      deptOptions.value = response.data || []
    } catch (error) {
      console.error('获取部门树失败:', error)
    }
  }

  /**
   * 通过条件过滤节点
   */
  function filterNode(value: string, data: Dept) {
    if (!value) return true
    return data.deptName?.indexOf(value) !== -1
  }

  /**
   * 节点单击事件
   */
  function handleNodeClick(_data: Dept) {
    // TODO: 更新useTable的查询参数
    refreshData()
  }

  /**
   * 重置搜索参数
   */
  function resetSearchParams() {
    searchForm.value = {
      userName: '',
      phonenumber: '',
      status: '',
      beginTime: '',
      endTime: ''
    }
    refreshData()
  }

  /**
   * 多选框选中数据
   */
  function handleSelectionChange(selection: User[]) {
    ids.value = selection.map((item) => item.userId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /**
   * 修改按钮操作
   */
  function handleUpdate(row?: User) {
    const userId = row?.userId || ids.value[0]
    if (userId) {
      showDialog('edit', { userId })
    }
  }

  /**
   * 删除按钮操作
   */
  async function handleDelete(row?: User) {
    const userIds = row?.userId ? [row.userId] : ids.value
    try {
      await ElMessageBox.confirm(`是否确认删除用户编号为"${userIds.join(',')}"的数据项？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await UserApi.delUser(userIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除用户失败:', error)
        ElMessage.error('删除用户失败')
      }
    }
  }

  /**
   * 用户状态修改
   */
  async function handleStatusChange(row: User) {
    const text = row.status === '0' ? '启用' : '停用'
    const userName = row.userName || row.nickName || '该'
    try {
      await ElMessageBox.confirm(`确认要"${text}""${userName}"用户吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await UserApi.changeUserStatus(row.userId!, row.status!)
      ElMessage.success(text + '成功')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('修改用户状态失败:', error)
        ElMessage.error('修改用户状态失败')
        // 恢复原状态
        row.status = row.status === '0' ? '1' : '0'
      }
    }
  }

  /**
   * 重置密码
   */
  async function handleResetPwd(row: User) {
    try {
      const { value: password } = await ElMessageBox.prompt(
        '请输入"' + row.userName + '"的新密码',
        '重置密码',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{5,20}$/,
          inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
        }
      )
      await UserApi.resetUserPwd(row.userId!, password)
      ElMessage.success('重置成功，新密码是：' + password)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('重置密码失败:', error)
        ElMessage.error('重置密码失败')
      }
    }
  }

  /**
   * 分配角色
   */
  function handleAuthRole(_row: User) {
    // TODO: 实现角色分配功能
    ElMessage.info('角色分配功能待实现')
  }

  /**
   * 导入用户
   */
  function handleImport() {
    // TODO: 实现用户导入功能
    ElMessage.info('用户导入功能待实现')
  }

  /**
   * 导出用户
   */
  async function handleExport() {
    try {
      const exportParams = {
        ...searchForm.value,
        deptId: undefined // TODO: 从部门树获取选中的部门ID
      }
      const blob = await UserApi.exportUser(exportParams)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `user_${new Date().getTime()}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出用户失败:', error)
      ElMessage.error('导出用户失败')
    }
  }

  /**
   * 显示弹窗
   */
  function showDialog(type: 'add' | 'edit', userData?: Partial<User>) {
    dialogType.value = type
    currentUserData.value = userData || {}
    dialogVisible.value = true
  }

  /**
   * 处理弹窗提交
   */
  function handleDialogSubmit() {
    dialogVisible.value = false
    refreshData()
  }

  /**
   * 处理搜索
   */
  function handleSearch() {
    // TODO: 更新useTable的查询参数
    refreshData()
  }

  // 监听部门名称变化，过滤部门树
  watch(deptName, (val) => {
    deptTreeRef.value?.filter(val)
  })

  // 组件挂载时获取数据
  onMounted(() => {
    getDeptTree()
  })
</script>

<style lang="scss" scoped>
  .user-container {
    box-sizing: border-box;
    display: flex;
    gap: 16px;
    height: 100%;

    .left-sidebar {
      flex-shrink: 0;
      width: 280px;
      height: 100%;
    }

    .right-content {
      flex-grow: 1;
      min-width: 0;
      height: 100%;
    }

    .art-table-card {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  @media screen and (max-width: $device-ipad) {
    .user-container {
      display: block;
      gap: 0;
      height: auto;

      .left-sidebar {
        width: 100%;
        height: auto;
        margin-bottom: 20px;
      }
    }
  }
</style>
